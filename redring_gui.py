import sys
import os
import threading
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import urllib3
import datetime
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 导入爬虫主要功能
# 注意：这里假设月份页面代码.py中的函数已经被重构为可导入的模块
# 在实际打包时，我们需要将其重构为模块
from 月份页面代码 import create_save_structure, main as crawler_main

class RedRingCrawlerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("小红圈页面爬取工具")
        self.root.geometry("600x400")
        self.root.resizable(True, True)

        # 设置样式
        self.style = ttk.Style()
        try:
            self.style.theme_use('clam')
        except:
            pass
        default_font = ('Segoe UI', 10)
        # 常规按钮
        self.style.configure('TButton', font=default_font, padding=6, relief='flat')
        # 强调按钮样式
        self.style.configure('Accent.TButton', font=default_font, padding=6, relief='flat', background='#4a90e2', foreground='#ffffff')
        self.style.map('Accent.TButton', background=[('active', '#357ab7')], foreground=[('disabled', '#bbbbbb')])
        # 其他控件字体和背景
        self.style.configure('TLabel', font=default_font)
        self.style.configure('TEntry', font=default_font)
        self.style.configure('TCheckbutton', font=default_font)
        self.style.configure('TLabelFrame', font=default_font, background='#f5f5f5')
        self.style.configure('TFrame', background='#f5f5f5')
        self.root.configure(bg='#f5f5f5')

        # 创建主框架
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 提示：需要输入网址和保存位置后方可登录和爬取，带"注意事项"按钮
        notice_frame = ttk.Frame(self.main_frame)
        notice_frame.pack(fill=tk.X, pady=(0,5))
        self.notice_label = ttk.Label(notice_frame, text="注意：请先输入网址和保存位置", foreground="red")
        self.notice_label.pack(side=tk.LEFT)
        self.notice_btn = ttk.Button(notice_frame, text="注意事项", command=self.show_notice)
        self.notice_btn.pack(side=tk.RIGHT)

        # URL输入
        self.url_frame = ttk.Frame(self.main_frame)
        self.url_frame.pack(fill=tk.X, pady=10)

        ttk.Label(self.url_frame, text="网址:").pack(side=tk.LEFT)
        self.url_var = tk.StringVar()
        self.url_entry = ttk.Entry(self.url_frame, textvariable=self.url_var, width=50)
        self.url_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 保存位置选择
        self.save_frame = ttk.Frame(self.main_frame)
        self.save_frame.pack(fill=tk.X, pady=10)

        ttk.Label(self.save_frame, text="保存位置:").pack(side=tk.LEFT)
        self.save_path_var = tk.StringVar()
        self.save_path_entry = ttk.Entry(self.save_frame, textvariable=self.save_path_var, width=40)
        self.save_path_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        self.browse_btn = ttk.Button(self.save_frame, text="浏览...", command=self.browse_save_location)
        self.browse_btn.pack(side=tk.LEFT, padx=5)

        # 高级选项框架
        self.options_frame = ttk.LabelFrame(self.main_frame, text="高级选项")
        # 高级选项默认显示
        self.options_frame.pack(fill=tk.X, pady=10)

        # 添加起始年月选择
        start_date_frame = ttk.Frame(self.options_frame)
        start_date_frame.grid(row=0, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(start_date_frame, text="起始年份:").pack(side=tk.LEFT, padx=(0, 5))
        self.start_year_var = tk.StringVar(value="")
        self.start_year_combo = ttk.Combobox(start_date_frame, textvariable=self.start_year_var, width=6)
        # 设置年份选项（从当前年份往前10年）
        current_year = datetime.datetime.now().year
        self.start_year_combo['values'] = [''] + [str(year) for year in range(current_year, current_year-11, -1)]
        self.start_year_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(start_date_frame, text="起始月份:").pack(side=tk.LEFT, padx=(0, 5))
        self.start_month_var = tk.StringVar(value="")
        self.start_month_combo = ttk.Combobox(start_date_frame, textvariable=self.start_month_var, width=4)
        # 设置月份选项
        self.start_month_combo['values'] = [''] + [str(month).zfill(2) for month in range(1, 13)]
        self.start_month_combo.pack(side=tk.LEFT)
        
        # 添加提示标签
        ttk.Label(start_date_frame, text="(留空表示从最新月份开始)", foreground="#666").pack(side=tk.LEFT, padx=(10, 0))

        # 是否保存所有年月选项
        self.save_all_months_var = tk.BooleanVar(value=True)
        self.save_all_months_check = ttk.Checkbutton(
            self.options_frame,
            text="保存所有年月的文章",
            variable=self.save_all_months_var
        )
        self.save_all_months_check.grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)

        # 是否生成全站搜索索引
        self.generate_search_index_var = tk.BooleanVar(value=True)
        self.generate_search_index_check = ttk.Checkbutton(
            self.options_frame,
            text="生成全站搜索索引",
            variable=self.generate_search_index_var
        )
        self.generate_search_index_check.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # 是否下载图片和媒体文件
        self.download_media_var = tk.BooleanVar(value=True)
        self.download_media_check = ttk.Checkbutton(
            self.options_frame,
            text="下载图片和媒体文件",
            variable=self.download_media_var
        )
        self.download_media_check.grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)

        # 是否修复CSS样式
        self.fix_css_var = tk.BooleanVar(value=True)
        self.fix_css_check = ttk.Checkbutton(
            self.options_frame,
            text="修复CSS样式",
            variable=self.fix_css_var
        )
        self.fix_css_check.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # 状态和进度显示
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_frame.pack(fill=tk.X, pady=10)

        ttk.Label(self.status_frame, text="状态:").pack(side=tk.LEFT)
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT, padx=5)

        # 进度条
        self.progress = ttk.Progressbar(self.main_frame, orient=tk.HORIZONTAL, length=100, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=10)

        # 日志显示区域
        self.log_frame = ttk.LabelFrame(self.main_frame, text="日志")
        self.log_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        self.log_text = tk.Text(self.log_frame, height=10, width=70, wrap=tk.WORD)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.scrollbar = ttk.Scrollbar(self.log_frame, command=self.log_text.yview)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=self.scrollbar.set)

        # 按钮区域
        self.button_frame = ttk.Frame(self.main_frame)
        self.button_frame.pack(fill=tk.X, pady=10)

        self.login_btn = ttk.Button(self.button_frame, text="登录微信", command=self.login_wechat, style='Accent.TButton')
        self.login_btn.pack(side=tk.RIGHT, padx=5)

        self.start_btn = ttk.Button(self.button_frame, text="开始爬取", command=self.start_crawling, style='Accent.TButton')
        self.start_btn.pack(side=tk.RIGHT, padx=5)
        self.start_btn.config(state=tk.DISABLED)  # 初始状态为禁用

        self.exit_btn = ttk.Button(self.button_frame, text="退出", command=self.root.destroy)
        self.exit_btn.pack(side=tk.RIGHT, padx=5)

        # 绑定回车键
        self.root.bind('<Return>', lambda event: self.start_crawling())

        # 初始化日志
        self.log("小红圈页面爬取工具已启动")
        self.log("请输入要爬取的网址，选择保存位置，然后点击\"开始爬取\"或按回车键")
        # 自动调整窗口大小以适配所有控件
        self.root.update_idletasks()
        width = self.root.winfo_reqwidth()
        height = self.root.winfo_reqheight()
        # 设置窗口初始尺寸并设置最小尺寸
        self.root.geometry(f"{width}x{height}")
        self.root.minsize(width, height)

    def browse_save_location(self):
        """打开文件对话框选择保存位置"""
        directory = filedialog.askdirectory(
            initialdir=self.save_path_var.get(),
            title="选择保存位置"
        )
        if directory:
            self.save_path_var.set(directory)

    def log(self, message):
        """向日志区域添加消息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)  # 自动滚动到最新消息

    def update_status(self, message):
        """更新状态栏消息"""
        self.status_var.set(message)
        self.log(message)

    def login_wechat(self):
        """打开浏览器并登录微信"""
        # 校验用户输入
        if not self.url_var.get().strip():
            messagebox.showerror("错误", "请输入有效的网址")
            return
        if not self.save_path_var.get().strip():
            messagebox.showerror("错误", "请选择保存位置")
            return
        # 禁用登录按钮，防止重复点击
        self.login_btn.config(state=tk.DISABLED)

        # 更新状态
        self.update_status("正在启动浏览器...")

        # 在新线程中运行浏览器，避免界面卡死
        threading.Thread(target=self._login_process, daemon=True).start()

    def _login_process(self):
        """微信登录过程"""
        try:
            # 初始化浏览器
            options = webdriver.ChromeOptions()
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-gpu')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument("--start-maximized")
            options.add_experimental_option('excludeSwitches', ['enable-automation'])
            options.add_experimental_option('useAutomationExtension', False)

            self.update_status("正在启动Chrome浏览器...")
            self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

            # 访问小红圈网站
            url = self.url_var.get().strip()
            self.update_status(f"正在访问: {url}")
            self.driver.get(url)

            # 提示用户登录
            self.update_status("请在浏览器中完成微信登录，然后按回车键开始爬取...")

            # 创建一个登录确认对话框
            self.root.after(0, self._show_login_confirmation)

        except Exception as e:
            self.update_status(f"启动浏览器失败: {str(e)}")
            self.log(f"详细错误: {e}")
            # 重新启用登录按钮
            self.root.after(0, lambda: self.login_btn.config(state=tk.NORMAL))

    def _show_login_confirmation(self):
        """自动确认登录，无需弹窗"""
        self.update_status("登录确认完成，可以开始爬取...")
        self.start_btn.config(state=tk.NORMAL)

    def start_crawling(self):
        """开始爬取过程"""
        # 获取用户输入
        url = self.url_var.get().strip()
        save_path = self.save_path_var.get().strip()

        # 验证输入
        if not url:
            messagebox.showerror("错误", "请输入有效的网址")
            return

        if not save_path:
            messagebox.showerror("错误", "请选择保存位置")
            return

        # 确保已经登录
        if not hasattr(self, 'driver') or self.driver is None:
            messagebox.showerror("错误", "请先点击\"登录微信\"按钮完成登录")
            return

        # 确保保存目录存在
        os.makedirs(save_path, exist_ok=True)

        # 禁用按钮，防止重复点击
        self.start_btn.config(state=tk.DISABLED)
        self.login_btn.config(state=tk.DISABLED)

        # 启动进度条
        self.progress.start()

        # 更新状态
        self.update_status("开始爬取页面...")

        # 在新线程中运行爬虫，避免界面卡死
        threading.Thread(target=self.run_crawler, args=(save_path,), daemon=True).start()

    def run_crawler(self, save_path):
        """在新线程中运行爬虫"""
        try:
            # 获取当前URL
            url = self.driver.current_url
            self.update_status(f"当前页面: {url}")

            # 创建保存目录结构
            self.update_status("创建保存目录结构...")
            save_dirs = create_save_structure(save_path)

            # 开始爬取页面
            self.update_status("开始爬取页面内容...")

            # 获取高级选项中的起始年份和月份
            start_year = self.start_year_var.get() if self.start_year_var.get() else None
            start_month = self.start_month_var.get() if self.start_month_var.get() else None

            self.update_status(f"使用起始年份: {start_year if start_year else '无'}, 起始月份: {start_month if start_month else '无'}")

            # 调用爬虫主函数
            try:
                crawler_main(
                    base_dir=save_path, 
                    url=url, 
                    driver=self.driver, 
                    start_year=start_year, 
                    start_month=start_month
                )
                # 动态获取模块路径，兼容打包后环境
                import importlib.util
                bundledir = getattr(sys, '_MEIPASS', os.path.dirname(os.path.abspath(__file__)))
                module_path = os.path.join(bundledir, "月份页面代码.py")
                spec = importlib.util.spec_from_file_location("月份页面代码", module_path)
                crawler_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(crawler_module)

                # 修改模块中的全局变量
                self.update_status("配置爬虫参数...")

                # 保存原始的全局变量值
                original_base_dir = getattr(crawler_module, 'base_dir', None)

                # 设置新的全局变量值
                setattr(crawler_module, 'base_dir', save_path)
                
                # 设置起始年月
                start_year = self.start_year_var.get().strip()
                start_month = self.start_month_var.get().strip()
                if start_year and start_month:
                    self.log(f"设置起始年月: {start_year}年{start_month}月")
                    setattr(crawler_module, 'start_year', start_year)
                    setattr(crawler_module, 'start_month', start_month)
                    self.update_status(f"将从 {start_year}年{start_month}月 开始爬取")
                else:
                    self.log("未设置起始年月，将从最新月份开始爬取")
                    # 如果存在这些属性，清除它们
                    if hasattr(crawler_module, 'start_year'):
                        delattr(crawler_module, 'start_year')
                    if hasattr(crawler_module, 'start_month'):
                        delattr(crawler_module, 'start_month')

                # 获取当前URL
                current_url = self.driver.current_url
                self.update_status(f"当前页面URL: {current_url}")

                # 替换原始的WebDriver实例
                # 注意：这是一个hack，因为原始代码中会创建新的WebDriver实例
                # 我们需要在调用main函数前保存当前的WebDriver实例
                self.update_status("准备开始爬取...")

                # 创建一个钩子函数来替换WebDriver的创建
                original_chrome = webdriver.Chrome

                def mock_chrome(*args, **kwargs):
                    self.update_status("使用已登录的浏览器实例...")
                    return self.driver

                # 替换WebDriver.Chrome构造函数
                webdriver.Chrome = mock_chrome

                # 替换input函数，避免程序等待用户输入
                import builtins
                original_input = builtins.input

                def mock_input(prompt=None):
                    if prompt and "登录完成后按回车继续" in prompt:
                        self.update_status("自动确认登录完成...")
                        return ""
                    return original_input(prompt)

                builtins.input = mock_input

                # 重定向 stdout 和 stderr 到 GUI 日志
                # 捕获日志回调函数
                log_fn = self.log
                class StdoutRedirector:
                    def __init__(self, fn):
                        self.fn = fn
                    def write(self, message):
                        if message and not message.isspace():
                            self.fn(message)
                    def flush(self):
                        pass
                original_stdout = sys.stdout
                original_stderr = sys.stderr
                sys.stdout = StdoutRedirector(log_fn)
                sys.stderr = StdoutRedirector(log_fn)

                try:
                    # 调用爬虫主函数，传入用户选择的保存路径和网址
                    self.update_status("开始爬取内容...")
                    # 获取用户输入的网址
                    url = self.url_var.get().strip()
                    if not url:
                        url = "https://www.red-ring.cn/group/9287"  # 默认网址

                    # 调用爬虫主函数，传入保存路径和网址
                    crawler_module.main(base_dir=save_path, url=url)
                    success = True
                finally:
                    # 恢复原始函数和变量
                    webdriver.Chrome = original_chrome
                    builtins.input = original_input
                    # 恢复 stdout 和 stderr
                    sys.stdout = original_stdout
                    sys.stderr = original_stderr

                    # 恢复原始全局变量
                    if original_base_dir is not None:
                        setattr(crawler_module, 'base_dir', original_base_dir)
            except Exception as e:
                self.update_status(f"爬取过程中发生错误: {str(e)}")
                import traceback
                self.log(traceback.format_exc())
                success = False

            if success:
                self.update_status("爬取完成！")
                messagebox.showinfo("完成", f"页面爬取完成，已保存到：\n{save_path}")
            else:
                self.update_status("爬取失败")
                messagebox.showerror("错误", "爬取过程失败，请查看日志了解详情")

        except Exception as e:
            self.update_status(f"发生错误: {str(e)}")
            messagebox.showerror("错误", f"爬取过程中发生错误：\n{str(e)}")
        finally:
            # 停止进度条
            self.root.after(0, self.progress.stop)
            # 重新启用按钮
            self.root.after(0, lambda: self.start_btn.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.login_btn.config(state=tk.NORMAL))

            # 关闭浏览器
            try:
                if hasattr(self, 'driver') and self.driver is not None:
                    self.driver.quit()
                    self.driver = None
            except:
                pass

    def show_notice(self):
        """弹出使用注意事项对话框"""
        messagebox.showinfo(
            "注意事项",
            '1. 浏览器需要全屏\n'
            '2. 微信登录后按回车或者点击开始爬取按钮\n'
            '3. 等待网页爬取完成，不要关闭软件\n'
            '4. 必须安装 Chrome 浏览器并确保可正常启动，否则爬虫无法运行\n\n'
            'Chrome 安装指南：\n'
            '  - 官方下载：访问 https://www.google.cn/chrome/ 下载并运行安装程序\n'
            '  - Winget 安装：在管理员 PowerShell 中运行：winget install Google.Chrome\n'
            '  - Chocolatey 安装：在管理员命令提示符中运行：choco install googlechrome -y'
        )

def main():
    root = tk.Tk()
    app = RedRingCrawlerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
