"""
爬虫适配器模块 - 将现有的爬虫代码与GUI界面连接
"""

import os
import sys
import time
import threading
import traceback
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import importlib.util

# 导入月份页面代码模块
def import_crawler_module(module_path):
    """动态导入爬虫模块"""
    try:
        spec = importlib.util.spec_from_file_location("crawler_module", module_path)
        crawler_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(crawler_module)
        return crawler_module
    except Exception as e:
        print(f"导入爬虫模块失败: {str(e)}")
        traceback.print_exc()
        return None

class CrawlerAdapter:
    """爬虫适配器类，用于连接GUI和爬虫代码"""
    
    def __init__(self, log_callback=None, status_callback=None):
        """初始化适配器
        
        Args:
            log_callback: 日志回调函数，用于向GUI发送日志消息
            status_callback: 状态回调函数，用于向GUI发送状态更新
        """
        self.log_callback = log_callback or (lambda msg: print(msg))
        self.status_callback = status_callback or (lambda msg: print(f"状态: {msg}"))
        self.driver = None
        self.crawler_module = None
        
        # 查找爬虫模块
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.crawler_path = os.path.join(script_dir, "月份页面代码.py")
        
        if not os.path.exists(self.crawler_path):
            self.log("错误: 找不到爬虫模块文件 '月份页面代码.py'")
            raise FileNotFoundError("找不到爬虫模块文件")
        
        # 导入爬虫模块
        self.crawler_module = import_crawler_module(self.crawler_path)
        if not self.crawler_module:
            self.log("错误: 导入爬虫模块失败")
            raise ImportError("导入爬虫模块失败")
        
        self.log("爬虫模块加载成功")
    
    def log(self, message):
        """发送日志消息"""
        if self.log_callback:
            self.log_callback(message)
    
    def update_status(self, status):
        """更新状态"""
        if self.status_callback:
            self.status_callback(status)
    
    def init_driver(self, headless=False):
        """初始化WebDriver"""
        self.update_status("正在初始化浏览器...")
        
        try:
            options = webdriver.ChromeOptions()
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-gpu')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument("--start-maximized")
            
            if headless:
                options.add_argument('--headless')
            
            options.add_experimental_option('excludeSwitches', ['enable-automation'])
            options.add_experimental_option('useAutomationExtension', False)
            options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
            
            # 使用webdriver_manager自动下载和管理ChromeDriver
            self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
            self.log("浏览器初始化成功")
            return True
        except Exception as e:
            self.log(f"浏览器初始化失败: {str(e)}")
            traceback.print_exc()
            return False
    
    def start_crawling(self, url, save_path, options=None):
        """开始爬取过程
        
        Args:
            url: 要爬取的网址
            save_path: 保存位置
            options: 爬取选项字典
        
        Returns:
            bool: 爬取是否成功
        """
        options = options or {}
        
        # 确保保存目录存在
        os.makedirs(save_path, exist_ok=True)
        
        # 初始化WebDriver
        if not self.init_driver():
            return False
        
        try:
            # 访问URL
            self.update_status(f"正在访问网址: {url}")
            self.driver.get(url)
            
            # 等待用户登录
            self.update_status("请在浏览器窗口中完成微信登录...")
            self.log("登录完成后，爬虫将自动继续")
            
            # 这里需要等待用户手动登录
            # 在GUI版本中，我们可以使用一个对话框来让用户确认登录完成
            # 在这个适配器中，我们使用一个简单的输入提示
            input_thread = threading.Thread(target=self._wait_for_login_input)
            input_thread.daemon = True
            input_thread.start()
            
            # 等待输入线程完成
            while input_thread.is_alive():
                time.sleep(0.5)
            
            # 检查登录状态
            if "login" in self.driver.current_url.lower():
                self.update_status("检测到登录页面，重新加载...")
                self.driver.get(url)
                time.sleep(2)
            
            # 创建保存目录结构
            self.update_status("创建保存目录结构...")
            save_dirs = self.crawler_module.create_save_structure(save_path)
            
            # 开始爬取
            self.update_status("开始爬取页面...")
            
            # 调用爬虫模块的主函数
            # 注意：这里需要修改原始爬虫代码，使其接受参数
            # 在实际实现中，我们可能需要修改原始代码或使用猴子补丁
            
            # 临时修改全局变量
            original_base_dir = getattr(self.crawler_module, 'base_dir', None)
            setattr(self.crawler_module, 'base_dir', save_path)
            
            # 调用爬虫主函数
            # 这里假设我们已经修改了原始代码，使其接受driver参数
            # 如果没有修改，我们需要使用猴子补丁或其他方法
            try:
                # 从options获取start_year和start_month
                start_year = options.get('start_year')
                start_month = options.get('start_month')

                # 尝试使用修改后的接口
                self.log(f"调用爬虫模块 main 函数，参数: start_year={start_year}, start_month={start_month}")
                result = self.crawler_module.main(driver=self.driver, base_dir=save_path, start_year=start_year, start_month=start_month)
            except TypeError as e:
                self.log(f"调用爬虫接口时发生TypeError: {e}。尝试不带年份月份参数调用...")
                # 如果原始函数不接受参数，使用原始接口
                self.log("使用原始爬虫接口 (不带年份月份参数)...")
                result = self.crawler_module.main(driver=self.driver, base_dir=save_path)
            
            # 恢复原始全局变量
            if original_base_dir is not None:
                setattr(self.crawler_module, 'base_dir', original_base_dir)
            
            self.update_status("爬取完成！")
            return True
            
        except Exception as e:
            self.log(f"爬取过程中发生错误: {str(e)}")
            traceback.print_exc()
            return False
        finally:
            # 关闭浏览器
            if self.driver:
                self.driver.quit()
    
    def _wait_for_login_input(self):
        """等待用户输入登录完成确认"""
        input("登录完成后按回车继续...")
        self.update_status("登录确认收到，继续爬取...")

# 测试代码
if __name__ == "__main__":
    def print_log(msg):
        print(f"[LOG] {msg}")
    
    def print_status(msg):
        print(f"[STATUS] {msg}")
    
    adapter = CrawlerAdapter(print_log, print_status)
    adapter.start_crawling("https://www.red-ring.cn/group/9287", "./redring_content")
