#!/usr/bin/env python3
"""激活码生成器脚本
生成机器激活码，使用方法：
  python activation_generator.py [--computer COMPUTER_NAME] [--user USER_NAME] [--machine-id MACHINE_ID]
如果不指定参数，则使用当前环境的 COMPUTERNAME 和 USERNAME。"""

import os
import hashlib
import argparse


def get_machine_id(computer_name=None, user_name=None):
    """根据计算机名和用户名生成机器 ID"""
    if computer_name is None:
        computer_name = os.environ.get('COMPUTERNAME', '')
    if user_name is None:
        user_name = os.environ.get('USERNAME', '')
    mid = f"{computer_name}_{user_name}"
    return hashlib.md5(mid.encode()).hexdigest()


def generate_activation_code(machine_id):
    """根据机器 ID 生成激活码"""
    raw = hashlib.md5(machine_id.encode()).hexdigest().upper()[:16]
    # 将 16 位字符串分为 4 段，每段 4 字符，用 '-' 连接
    return '-'.join(raw[i:i+4] for i in range(0, 16, 4))


def main():
    parser = argparse.ArgumentParser(description="小红圈激活码生成器")
    parser.add_argument('--computer', help="计算机名 (默认为当前环境 COMPUTERNAME)")
    parser.add_argument('--user', help="用户名 (默认为当前环境 USERNAME)")
    parser.add_argument('--machine-id', help="直接指定机器 ID，优先于 --computer/--user")
    args = parser.parse_args()

    if args.machine_id:
        mid = args.machine_id
    else:
        mid = get_machine_id(args.computer, args.user)

    code = generate_activation_code(mid)
    print(f"Machine ID: {mid}")
    print(f"Activation Code: {code}")


if __name__ == "__main__":
    main() 