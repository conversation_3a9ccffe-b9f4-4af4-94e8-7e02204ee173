"""
小红圈页面爬取工具 - 主程序
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import threading
import traceback
import datetime

# 导入GUI和适配器模块
from redring_gui import RedRingCrawlerGUI
from crawler_adapter import CrawlerAdapter

# 导入过期检查模块
from expiry_check import check_expiry, show_expiry_message

class EnhancedRedRingCrawlerGUI(RedRingCrawlerGUI):
    """增强版GUI，集成了爬虫适配器"""

    def __init__(self, root):
        """初始化增强版GUI"""
        super().__init__(root)

        # 初始化爬虫适配器
        try:
            self.adapter = CrawlerAdapter(
                log_callback=self.log,
                status_callback=self.update_status
            )
            self.log("爬虫适配器初始化成功")

            # 显示到期信息
            expiry_status = check_expiry()
            if not expiry_status['expired']:
                self.log(f"当前时间: {expiry_status.get('current_date', '未知')}")
                self.log(f"到期时间: {expiry_status.get('expiry_date', '未知')}")
                self.log(f"距离到期还剩 {expiry_status['days_left']} 天 {expiry_status['hours_left']} 小时 {expiry_status['minutes_left']} 分钟 {expiry_status['seconds_left']} 秒")

        except Exception as e:
            self.log(f"爬虫适配器初始化失败: {str(e)}")
            messagebox.showerror("错误", f"爬虫适配器初始化失败：\n{str(e)}")
            # 禁用开始按钮
            self.start_btn.config(state=tk.DISABLED)

    def run_crawler(self, save_path):
        """运行爬虫（覆盖父类方法）"""
        try:
            # 再次检查软件是否过期
            expiry_status = check_expiry()
            if expiry_status['expired']:
                self.update_status("软件已过期，无法继续使用")
                messagebox.showerror("软件已过期", "小红圈页面爬取工具的试用期（15天）已结束。\n请联系开发者获取完整版。")
                return

            # 直接调用父类方法继续爬取，重用已登录的浏览器实例
            super().run_crawler(save_path)
            return

        except Exception as e:
            self.update_status(f"发生错误: {str(e)}")
            self.log(traceback.format_exc())
            messagebox.showerror("错误", f"爬取过程中发生错误：\n{str(e)}")
        finally:
            # 停止进度条
            self.root.after(0, self.progress.stop)
            # 重新启用按钮
            self.root.after(0, lambda: self.start_btn.config(state=tk.NORMAL))
            # 重新启用登录按钮
            self.root.after(0, lambda: self.login_btn.config(state=tk.NORMAL))

def check_environment():
    """检查运行环境"""
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("错误: 需要Python 3.6或更高版本")
        messagebox.showerror("错误", "需要Python 3.6或更高版本")
        return False

    # 检查必要的模块
    required_modules = [
        "selenium",
        "bs4",
        "requests",
        "urllib3",
        "tqdm"
    ]

    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        error_msg = f"缺少必要的模块: {', '.join(missing_modules)}\n请安装这些模块后再运行程序"
        print(f"错误: {error_msg}")
        messagebox.showerror("错误", error_msg)
        return False

    return True

def main():
    """主函数"""
    # 检查环境
    if not check_environment():
        return

    # 显示到期信息
    expiry_status = check_expiry()
    print(f"当前时间: {expiry_status.get('current_date')}")
    print(f"到期时间: {expiry_status.get('expiry_date')}")
    print(f"距离到期还剩 {expiry_status['days_left']} 天 {expiry_status['hours_left']} 小时 {expiry_status['minutes_left']} 分钟 {expiry_status['seconds_left']} 秒")

    # 创建GUI
    root = tk.Tk()

    # 检查软件是否过期
    if not show_expiry_message(root):
        # 如果软件已过期，退出程序
        root.destroy()
        return

    app = EnhancedRedRingCrawlerGUI(root)

    # 设置窗口图标（如果存在）
    icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "icon.ico")
    if os.path.exists(icon_path):
        root.iconbitmap(icon_path)

    # 运行主循环
    root.mainloop()

if __name__ == "__main__":
    main()
