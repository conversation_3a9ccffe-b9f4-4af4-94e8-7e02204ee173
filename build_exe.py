"""
打包脚本 - 将GUI和爬虫代码打包成可执行文件
"""

import os
import sys
import shutil
import subprocess
import platform

def check_requirements():
    """检查打包所需的依赖是否已安装"""
    print("检查打包依赖...")
    
    try:
        import PyInstaller
        print("PyInstaller 已安装")
    except ImportError:
        print("PyInstaller 未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # 检查其他依赖
    dependencies = [
        "selenium",
        "webdriver_manager",
        "requests",
        "beautifulsoup4",
        "urllib3",
        "tqdm"
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"{dep} 已安装")
        except ImportError:
            print(f"{dep} 未安装，正在安装...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])

def create_spec_file():
    """创建PyInstaller规范文件"""
    spec_content = """# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['redring_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('月份页面代码.py', '.'),
        ('crawler_adapter.py', '.'),
    ],
    hiddenimports=[
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.common.keys',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support.expected_conditions',
        'selenium.common.exceptions',
        'webdriver_manager',
        'webdriver_manager.chrome',
        'bs4',
        'urllib3',
        'requests',
        'tqdm',
        'concurrent.futures',
        'hashlib',
        'json',
        're',
        'datetime',
        'functools',
        'collections',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='小红圈页面爬取工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico',
)
"""
    
    with open("redring.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("PyInstaller规范文件已创建")

def create_icon():
    """创建一个简单的图标文件"""
    # 如果没有图标文件，我们可以使用一个默认图标
    # 这里我们只是检查图标是否存在，如果不存在，提示用户
    if not os.path.exists("icon.ico"):
        print("警告: 未找到图标文件 'icon.ico'")
        print("将使用默认图标")
        
        # 在实际应用中，我们可以从网络下载一个图标，或者使用一个内置的图标
        # 这里为了简单起见，我们只是提示用户

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    # 检查规范文件是否存在
    if not os.path.exists("redring.spec"):
        create_spec_file()
    
    # 创建图标
    create_icon()
    
    # 运行PyInstaller
    subprocess.check_call([
        sys.executable, 
        "-m", 
        "PyInstaller", 
        "redring.spec", 
        "--clean"
    ])
    
    print("可执行文件构建完成")

def create_installer():
    """创建安装程序（仅Windows平台）"""
    if platform.system() != "Windows":
        print("创建安装程序功能仅支持Windows平台")
        return
    
    print("开始创建安装程序...")
    
    # 检查Inno Setup是否已安装
    inno_setup_path = r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
    if not os.path.exists(inno_setup_path):
        print("未找到Inno Setup，跳过创建安装程序")
        print("如需创建安装程序，请安装Inno Setup: https://jrsoftware.org/isdl.php")
        return
    
    # 创建Inno Setup脚本
    iss_content = """
#define MyAppName "小红圈页面爬取工具"
#define MyAppVersion "1.0"
#define MyAppPublisher "小红圈爬虫"
#define MyAppURL "https://github.com/yourusername/redring-crawler"
#define MyAppExeName "小红圈页面爬取工具.exe"

[Setup]
AppId={{8A95C16D-5B41-4A5D-9B3C-DFCF98C7A1E2}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\\{#MyAppName}
DisableProgramGroupPage=yes
LicenseFile=LICENSE.txt
OutputDir=installer
OutputBaseFilename=小红圈页面爬取工具_安装程序
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "chinesesimplified"; MessagesFile: "compiler:Languages\\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "dist\\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{autoprograms}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"
Name: "{autodesktop}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent
"""
    
    # 创建LICENSE.txt文件（如果不存在）
    if not os.path.exists("LICENSE.txt"):
        with open("LICENSE.txt", "w", encoding="utf-8") as f:
            f.write("""小红圈页面爬取工具许可协议

本软件仅供个人学习和研究使用，请勿用于商业目的。
使用本软件时，请遵守相关法律法规和网站的使用条款。
""")
    
    # 创建installer目录
    os.makedirs("installer", exist_ok=True)
    
    # 写入Inno Setup脚本
    with open("redring_installer.iss", "w", encoding="utf-8") as f:
        f.write(iss_content)
    
    # 运行Inno Setup编译器
    subprocess.check_call([inno_setup_path, "redring_installer.iss"])
    
    print("安装程序创建完成")

def main():
    """主函数"""
    print("小红圈页面爬取工具打包脚本")
    print("==========================")
    
    # 检查依赖
    check_requirements()
    
    # 构建可执行文件
    build_executable()
    
    # 创建安装程序（仅Windows平台）
    if platform.system() == "Windows":
        create_installer()
    
    print("打包过程完成")
    print(f"可执行文件位于: {os.path.abspath('dist/小红圈页面爬取工具.exe')}")
    
    if platform.system() == "Windows" and os.path.exists("installer/小红圈页面爬取工具_安装程序.exe"):
        print(f"安装程序位于: {os.path.abspath('installer/小红圈页面爬取工具_安装程序.exe')}")

if __name__ == "__main__":
    main()
