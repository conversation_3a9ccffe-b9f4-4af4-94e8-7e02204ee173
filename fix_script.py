#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 此脚本用于修复月份页面代码.py中的global声明问题
import re

def fix_global_declaration():
    print("开始修复global声明问题...")
    
    # 读取文件
    with open('月份页面代码.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 在函数开始处添加所有全局变量声明
    main_func_pattern = r'def main\(base_dir=None, url=None, driver=None, start_year=None, start_month=None\):(.*?)# 导入os模块'
    
    def add_global_declarations(match):
        func_start = match.group(0)
        # 添加global声明到函数开头
        modified = re.sub(
            r'(""".*?"""\s+)',
            r'\1    # 声明所有全局变量\n    global latest_year, latest_month, main_index_generated, is_last_month\n\n',
            func_start,
            flags=re.DOTALL
        )
        return modified
    
    # 应用替换
    content = re.sub(main_func_pattern, add_global_declarations, content, flags=re.DOTALL)
    
    # 删除后面重复的global声明
    content = content.replace(
        '    # 检查是否设置了起始年月(使用global变量latest_year和latest_month)\n    global latest_year, latest_month',
        '    # 检查是否设置了起始年月(使用global变量latest_year和latest_month)'
    )
    
    # 删除函数中间的重复声明
    content = content.replace(
        '    global main_index_generated      # ★新增：声明使用全局变量\n    global is_last_month             # 新增：标记是否是最后一个月份',
        '    # 全局变量已在函数开头声明'
    )
    
    # 写回文件
    with open('月份页面代码.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("修复完成!")

if __name__ == "__main__":
    fix_global_declaration() 