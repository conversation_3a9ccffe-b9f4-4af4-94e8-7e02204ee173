# 起始年月功能使用说明

## 功能概述

现在小红圈页面爬取工具支持从指定的年份和月份开始爬取，而不是从最新的内容开始。这个功能可以帮助您：

1. **节省时间**：跳过不需要的新内容，直接从感兴趣的时间点开始
2. **精确控制**：只爬取特定时间段的内容
3. **增量更新**：从上次爬取停止的地方继续

## 使用方法

### 方法1：通过GUI界面设置

1. 启动小红圈页面爬取工具
2. 在"高级选项"区域找到"起始年份"和"起始月份"设置
3. 设置您想要开始爬取的年份（如：2024）
4. 设置您想要开始爬取的月份（如：12）
5. 点击"登录微信"完成登录
6. 点击"开始爬取"开始从指定年月爬取

### 方法2：直接调用Python函数

```python
from 月份页面代码 import main

# 从2024年12月开始爬取
main(
    base_dir="./redring_content",
    url="https://www.red-ring.cn/group/9287",
    start_year="2024",
    start_month="12"
)
```

### 方法3：通过命令行参数

```bash
python 月份页面代码.py --start-year 2024 --start-month 12
```

## 功能特点

### 1. 智能过滤
- **年份过滤**：只处理指定年份及之前的年份
- **月份过滤**：在指定年份中，只处理指定月份及之前的月份
- **顺序处理**：按照从新到旧的顺序处理（年份从大到小，月份从大到小）

### 2. 灵活配置
- **可选参数**：如果不设置起始年月，将处理所有可用内容
- **格式宽松**：支持多种年月格式（如："2024"、"12"、"03"等）
- **错误处理**：如果指定的年月不存在，会自动处理所有内容并给出提示

### 3. 状态记录
- **全局变量**：自动记录最新处理的年月信息
- **进度显示**：清晰显示当前处理的年份和月份
- **日志输出**：详细的处理日志，便于跟踪进度

## 使用示例

### 示例1：从2024年6月开始爬取
```python
main(start_year="2024", start_month="6")
```
这将处理：2024年6月、2024年5月、2024年4月...直到最早的内容

### 示例2：只爬取2023年的内容
```python
main(start_year="2023", start_month="12")
```
这将处理：2023年12月、2023年11月...2023年1月，然后停止

### 示例3：从特定月份开始的增量更新
```python
# 第一次爬取：从2024年1月开始
main(start_year="2024", start_month="1")

# 后续更新：从2024年6月开始（跳过已爬取的1-5月）
main(start_year="2024", start_month="6")
```

## 注意事项

### 1. 参数格式
- **年份**：4位数字字符串，如"2024"
- **月份**：1-2位数字字符串，如"1"、"12"、"03"
- **大小写**：参数名区分大小写

### 2. 处理逻辑
- 爬虫会按照从新到旧的顺序处理内容
- 如果指定的年月不存在，会跳过并继续处理其他年月
- 处理完指定年月后，会继续处理更早的内容

### 3. 性能考虑
- 设置起始年月可以显著减少爬取时间
- 建议根据实际需要设置合适的起始时间
- 如果只需要最新内容，可以不设置起始年月

## 错误处理

### 常见错误及解决方法

1. **年月格式错误**
   ```
   警告：起始年月格式不正确，将忽略设置: abc年def月
   ```
   解决：使用正确的数字格式，如"2024"和"12"

2. **指定年月不存在**
   ```
   未找到指定的起始年份 2025，将处理全部年份
   ```
   解决：检查网站上是否存在该年月的内容

3. **参数传递错误**
   ```
   TypeError: main() got an unexpected keyword argument
   ```
   解决：确保使用正确的参数名：start_year 和 start_month

## 技术实现

### 核心函数修改
1. **main函数**：增加start_year和start_month参数
2. **save_redring函数**：增加年月过滤逻辑
3. **GUI界面**：增加起始年月设置控件

### 过滤算法
```python
# 年份过滤
if start_year:
    start_year_int = int(start_year)
    filtered_years = [y for y in years if int(y) >= start_year_int]

# 月份过滤（在指定年份中）
if start_year and start_month and current_year == start_year:
    start_month_int = int(start_month)
    filtered_months = [m for m in months if int(m.replace('月', '')) >= start_month_int]
```

## 更新日志

- **v1.0**：初始实现起始年月功能
- **v1.1**：增加GUI界面支持
- **v1.2**：优化错误处理和日志输出
- **v1.3**：增加命令行参数支持

## 反馈与支持

如果您在使用过程中遇到问题或有改进建议，请：
1. 检查日志输出中的错误信息
2. 确认年月格式是否正确
3. 验证网站上是否存在指定的年月内容
4. 联系技术支持获取帮助
