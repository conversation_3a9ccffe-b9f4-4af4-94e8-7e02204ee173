# 小红圈页面爬取工具

这是一个用于爬取小红圈页面内容的工具，提供了图形界面，使得操作更加简便。

## 功能特点

- 图形用户界面，操作简单直观
- 支持选择保存位置
- 支持微信登录
- 自动保存文章、图片、音频等内容
- 生成全站搜索索引，方便离线查找内容
- 支持按年月浏览文章
- 支持一键打包成可执行程序，无需安装Python环境

## 系统要求

- Windows 7/8/10/11
- 或 macOS 10.12+
- 或 Linux (Ubuntu 18.04+)
- 联网环境
- Chrome浏览器（程序会自动下载适配的ChromeDriver）

## 安装方法

### 方法一：使用安装程序（仅Windows）

1. 下载最新的安装程序 `小红圈页面爬取工具_安装程序.exe`
2. 双击运行安装程序，按照提示完成安装
3. 从开始菜单或桌面快捷方式启动程序

### 方法二：直接运行可执行文件

1. 下载最新的可执行文件 `小红圈页面爬取工具.exe`（Windows）或 `小红圈页面爬取工具`（macOS/Linux）
2. 双击运行程序

### 方法三：从源代码运行

如果您有Python环境（3.6+），可以直接从源代码运行：

1. 克隆或下载本仓库
2. 安装依赖：`pip install -r requirements.txt`
3. 运行主程序：`python main.py`

## 使用方法

1. 启动程序后，在"网址"输入框中输入要爬取的小红圈页面URL
   - 默认为 `https://www.red-ring.cn/group/9287`
   - 您可以替换为其他小红圈页面的URL

2. 在"保存位置"输入框中选择保存内容的目录
   - 默认为桌面上的 `redring_content` 文件夹
   - 点击"浏览..."按钮可以选择其他位置

3. 根据需要调整高级选项：
   - 保存所有年月的文章：是否爬取所有年月的文章
   - 生成全站搜索索引：是否生成全站搜索功能所需的索引文件
   - 下载图片和媒体文件：是否下载文章中的图片、音频等媒体文件
   - 修复CSS样式：是否修复页面样式问题

4. 点击"开始爬取"按钮或按回车键开始爬取过程

5. 程序会打开Chrome浏览器窗口，请在浏览器中完成微信登录
   - 登录完成后，程序会自动继续爬取过程
   - 爬取过程中，可以在日志区域查看进度和状态

6. 爬取完成后，会弹出提示框，点击确定关闭提示

7. 爬取的内容保存在指定的目录中，可以通过浏览器打开 `index.html` 文件查看

## 目录结构说明

爬取完成后，保存目录中会生成以下文件和文件夹：

- `index.html`：主页面，包含年月导航和最新文章列表
- `all_articles.json`：全站文章索引文件，用于搜索功能
- `css/`：样式文件目录
- `js/`：JavaScript脚本目录
- `images/`：图片文件目录
- `media/`：音频等媒体文件目录
- `pages/`：文章页面目录，每篇文章一个子目录
- `years/`：按年月组织的文章目录

## 离线浏览说明

1. 打开保存目录中的 `index.html` 文件
2. 可以通过年月导航浏览不同时期的文章
3. 可以使用搜索框搜索全站文章
4. 可以使用日期搜索功能按日期范围查找文章

## 常见问题

1. **Q: 程序无法启动怎么办？**
   A: 请确保您的系统满足最低要求，并且已安装Chrome浏览器。如果使用源代码运行，请确保已安装所有依赖。

2. **Q: 微信登录后程序没有继续运行怎么办？**
   A: 请确保您已完成微信登录，并且已回到小红圈页面。然后点击程序中的"继续"按钮。

3. **Q: 爬取过程中出现错误怎么办？**
   A: 请查看日志区域的错误信息，常见原因包括网络问题、登录失效等。您可以尝试重新启动程序并再次登录。

4. **Q: 保存的页面样式不正确怎么办？**
   A: 请确保勾选了"修复CSS样式"选项，或者手动运行修复命令：`python 月份页面代码.py fix_css --dir=保存目录`

5. **Q: 如何更新程序？**
   A: 下载最新版本的程序并替换旧版本，或者重新运行安装程序。

## 开发者信息

如需修改或定制程序，请参考以下文件：

- `main.py`：主程序入口
- `redring_gui.py`：图形界面实现
- `crawler_adapter.py`：爬虫适配器，连接GUI和爬虫代码
- `月份页面代码.py`：爬虫核心代码
- `build_exe.py`：打包脚本，用于生成可执行文件

## 打包说明

如果您想自己打包程序，可以使用以下命令：

```bash
python build_exe.py
```

这将生成可执行文件和安装程序（仅Windows平台）。

## 许可说明

本软件仅供个人学习和研究使用，请勿用于商业目的。使用本软件时，请遵守相关法律法规和网站的使用条款。
