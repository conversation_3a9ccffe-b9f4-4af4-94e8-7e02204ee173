"""
过期检查模块 - 限制软件使用期限为15天
"""

import os
import time
import json
import hashlib
import datetime
from tkinter import messagebox
import platform

# 安装日期文件名
INSTALL_DATE_FILE = "app_data.dat"

# 固定到期日期（年,月,日,时,分,秒）
FIXED_EXPIRY_DATE = datetime.datetime(2025, 6, 1, 23, 59, 59)

def get_machine_id():
    """获取机器唯一标识符"""
    # 获取计算机名和用户名
    computer_name = os.environ.get('COMPUTERNAME', '')
    user_name = os.environ.get('USERNAME', '')

    # 创建唯一标识
    machine_id = f"{computer_name}_{user_name}"

    # 使用哈希函数生成固定长度的标识符
    return hashlib.md5(machine_id.encode()).hexdigest()

def encrypt_data(data):
    """简单加密数据"""
    machine_id = get_machine_id()
    key = hashlib.sha256(machine_id.encode()).digest()

    # 将数据转换为JSON字符串
    json_data = json.dumps(data)

    # 简单的XOR加密
    encrypted = []
    for i, char in enumerate(json_data):
        key_char = key[i % len(key)]
        encrypted.append(chr(ord(char) ^ key_char))

    return ''.join(encrypted)

def decrypt_data(encrypted_data):
    """简单解密数据"""
    machine_id = get_machine_id()
    key = hashlib.sha256(machine_id.encode()).digest()

    # 简单的XOR解密
    decrypted = []
    for i, char in enumerate(encrypted_data):
        key_char = key[i % len(key)]
        decrypted.append(chr(ord(char) ^ key_char))

    # 将JSON字符串转换回数据
    try:
        return json.loads(''.join(decrypted))
    except:
        return None

def get_app_data_dir():
    """获取应用数据目录"""
    # 在Windows上使用 %APPDATA% 目录存储数据，其他平台使用用户主目录下隐藏目录
    if platform.system() == "Windows":
        base_dir = os.environ.get('APPDATA', os.path.expanduser('~'))
        app_data_dir = os.path.join(base_dir, 'redring_crawler')
    else:
        app_data_dir = os.path.join(os.path.expanduser('~'), '.redring_crawler')
    os.makedirs(app_data_dir, exist_ok=True)
    return app_data_dir

def save_install_date(force=False):
    """保存安装日期

    Args:
        force: 如果为True，即使文件已存在也会覆盖它
    """
    app_data_dir = get_app_data_dir()
    install_date_path = os.path.join(app_data_dir, INSTALL_DATE_FILE)

    # 如果文件已存在且不强制覆盖，则直接返回
    if os.path.exists(install_date_path) and not force:
        print(f"安装日期文件已存在，不覆盖: {install_date_path}")
        return False

    # 获取当前日期时间，精确到秒
    current_datetime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 创建数据
    data = {
        'install_date': current_datetime,
        'machine_id': get_machine_id()
    }

    print(f"{'覆盖' if force and os.path.exists(install_date_path) else '创建'}安装日期文件: {install_date_path}, 时间: {current_datetime}")

    # 加密数据
    encrypted_data = encrypt_data(data)

    # 保存到文件
    with open(install_date_path, 'w', encoding='utf-8') as f:
        f.write(encrypted_data)

def get_install_date():
    """获取安装日期"""
    app_data_dir = get_app_data_dir()
    install_date_path = os.path.join(app_data_dir, INSTALL_DATE_FILE)
    print(f"DEBUG: 安装日期文件路径: {install_date_path}")

    # 如果文件不存在，创建它
    if not os.path.exists(install_date_path):
        print("安装日期文件不存在，创建新文件")
        save_install_date()  # 这里不需要force=True，因为文件不存在

        # 重新读取刚创建的文件
        try:
            with open(install_date_path, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
            data = decrypt_data(encrypted_data)
            if data and data.get('install_date'):
                return data.get('install_date')
        except:
            pass

        # 如果读取失败，返回当前时间
        return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 读取文件
    try:
        with open(install_date_path, 'r', encoding='utf-8') as f:
            encrypted_data = f.read()

        # 解密数据
        data = decrypt_data(encrypted_data)

        # 验证机器ID
        if data and data.get('machine_id') == get_machine_id():
            install_date = data.get('install_date')
            # 如果是旧格式（只有日期没有时间），添加时间部分
            if ' ' not in install_date:
                install_date += ' 00:00:00'
            return install_date
        else:
            # 如果机器ID不匹配，可能是文件被复制到其他机器
            # 强制重新创建安装日期文件
            print("机器ID不匹配，强制重新创建安装日期文件")
            save_install_date(force=True)

            # 重新读取刚创建的文件
            try:
                with open(install_date_path, 'r', encoding='utf-8') as f:
                    encrypted_data = f.read()
                data = decrypt_data(encrypted_data)
                if data and data.get('install_date'):
                    return data.get('install_date')
            except:
                pass

            # 如果读取失败，返回当前时间
            return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    except Exception as e:
        # 如果读取失败，强制重新创建安装日期文件
        print(f"读取安装日期文件失败: {str(e)}，强制重新创建")
        save_install_date(force=True)

        # 重新读取刚创建的文件
        try:
            with open(install_date_path, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
            data = decrypt_data(encrypted_data)
            if data and data.get('install_date'):
                return data.get('install_date')
        except:
            pass

        # 如果读取失败，返回当前时间
        return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def check_expiry():
    """检查软件是否过期（固定到期日期）"""
    current_date = datetime.datetime.now()
    expiry_date = FIXED_EXPIRY_DATE
    if current_date > expiry_date:
        expired = True
        days_left = hours_left = minutes_left = seconds_left = 0
    else:
        diff = expiry_date - current_date
        days_left = diff.days
        seconds = diff.seconds
        hours_left = seconds // 3600
        minutes_left = (seconds % 3600) // 60
        seconds_left = seconds % 60
        expired = False
    return {
        'expired': expired,
        'days_left': days_left,
        'hours_left': hours_left,
        'minutes_left': minutes_left,
        'seconds_left': seconds_left,
        'current_date': current_date.strftime('%Y-%m-%d %H:%M:%S'),
        'expiry_date': expiry_date.strftime('%Y-%m-%d %H:%M:%S')
    }

def show_expiry_message(root=None):
    """显示过期消息 - 固定到期日期"""
    expiry_status = check_expiry()

    # 格式化详细的时间信息
    time_details = (
        f"当前时间: {expiry_status.get('current_date', '未知')}\n"
        f"到期时间: {expiry_status.get('expiry_date', '未知')}\n"
    )

    # 格式化剩余时间
    if not expiry_status['expired']:
        remaining_time = (
            f"剩余时间: {expiry_status['days_left']}天 "
            f"{expiry_status['hours_left']}小时 "
            f"{expiry_status['minutes_left']}分钟 "
            f"{expiry_status['seconds_left']}秒"
        )
    else:
        remaining_time = "已到期"

    if expiry_status['expired']:
        # 到期后显示错误
        messagebox.showerror(
            "软件已过期",
            f"小红圈页面爬取工具已于 {expiry_status.get('expiry_date', '未知')} 到期。\n\n{time_details}请联系开发者获取完整版。"
        )
        return False
    elif expiry_status['days_left'] <= 3:
        # 即将到期时显示警告
        messagebox.showwarning(
            "软件即将到期",
            f"小红圈页面爬取工具将于 {expiry_status.get('expiry_date', '未知')} 到期。\n\n{time_details}{remaining_time}\n\n请联系开发者获取完整版。"
        )
        return True
    else:
        # 剩余足够时打印信息
        print(f"当前时间: {expiry_status.get('current_date', '未知')}，到期时间: {expiry_status.get('expiry_date', '未知')}")
        print(f"距离到期还剩 {remaining_time}")
        return True

if __name__ == "__main__":
    # 测试
    save_install_date()
    print(f"安装日期: {get_install_date()}")
    print(f"过期状态: {check_expiry()}")
